{"numTotalTestSuites": 2, "numPassedTestSuites": 0, "numFailedTestSuites": 2, "numPendingTestSuites": 0, "numTotalTests": 6, "numPassedTests": 1, "numFailedTests": 5, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1753820898015, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["GlobalTargetPriceManager"], "fullName": "GlobalTargetPriceManager should render the component", "status": "passed", "title": "should render the component", "duration": 69.31092400000034, "failureMessages": [], "location": {"line": 68, "column": 3}, "meta": {}}, {"ancestorTitles": ["GlobalTargetPriceManager"], "fullName": "GlobalTargetPriceManager should load and display current global target price with correct decimal precision", "status": "failed", "title": "should load and display current global target price with correct decimal precision", "duration": 3177.8786150000005, "failureMessages": ["Error: Unable to find an element with the text: 2.0 USDT per 1 BLOCKS. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"bg-white/70 backdrop-blur-sm border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 pb-0 pb-4\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center space-x-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-dollar-sign h-5 w-5 text-blue-600\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<line\u001b[39m\n              \u001b[33mx1\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mx2\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33my1\u001b[39m=\u001b[32m\"2\"\u001b[39m\n              \u001b[33my2\u001b[39m=\u001b[32m\"22\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<h3\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-lg font-semibold text-gray-900\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mGlobal Target Price Management\u001b[0m\n          \u001b[36m</h3>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-start space-x-2 mt-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-info h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<circle\u001b[39m\n              \u001b[33mcx\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mcy\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mr\u001b[39m=\u001b[32m\"10\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 16v-4\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 8h.01\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<p\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm text-blue-700\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mThe global target price is used exclusively for liquidity pool operations. It does not affect user-facing exchange rates, which are set per package.\u001b[0m\n          \u001b[36m</p>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 space-y-6\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"bg-gray-50 rounded-lg p-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<h4\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm font-medium text-gray-700 mb-2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mCurrent Global Target Price\u001b[0m\n          \u001b[36m</h4>\u001b[39m\n          \u001b[36m<p\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-2xl font-bold text-gray-900\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0m2.0\u001b[0m\n            \u001b[0m \u001b[0m\n            \u001b[36m<span\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm font-normal text-gray-600\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mUSDT per BLOCKS\u001b[0m\n            \u001b[36m</span>\u001b[39m\n          \u001b[36m</p>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<label\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block text-sm font-medium text-gray-700\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[0mNew Global Target Price (USDT per 1 BLOCKS)\u001b[0m\n              \u001b[36m</label>\u001b[39m\n              \u001b[36m<input\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 min-h-[44px] text-base md:text-sm placeholder-gray-400 shadow-sm transition-colors focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500\"\u001b[39m\n                \u001b[33mmax\u001b[39m=\u001b[32m\"100\"\u001b[39m\n                \u001b[33mmin\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mplaceholder\u001b[39m=\u001b[32m\"2.0\"\u001b[39m\n                \u001b[33mstep\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mtype\u001b[39m=\u001b[32m\"number\"\u001b[39m\n                \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n              \u001b[36m/>\u001b[39m\n            \u001b[36m</div>\u001b[39m\n            \u001b[36m<p\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"text-xs text-gray-500 mt-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mThis price is used for liquidity pool operations only. It does not affect user token allocations.\u001b[0m\n            \u001b[36m</p>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<button\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500 px-6 py-3 text-sm min-h-[44px] opacity-50 cursor-not-allowed w-full\"\u001b[39m\n            \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mUpdate Global Target Price\u001b[0m\n          \u001b[36m</button>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Proxy.waitForWrapper (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/wait-for.js:163:27)\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/components/GlobalTargetPriceManager.test.tsx:84:11\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "Error: Unable to find an element with the text: 2.0 USDT per 1 BLOCKS. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"bg-white/70 backdrop-blur-sm border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 pb-0 pb-4\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center space-x-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-dollar-sign h-5 w-5 text-blue-600\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<line\u001b[39m\n              \u001b[33mx1\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mx2\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33my1\u001b[39m=\u001b[32m\"2\"\u001b[39m\n              \u001b[33my2\u001b[39m=\u001b[32m\"22\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<h3\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-lg font-semibold text-gray-900\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mGlobal Target Price Management\u001b[0m\n          \u001b[36m</h3>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-start space-x-2 mt-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-info h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<circle\u001b[39m\n              \u001b[33mcx\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mcy\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mr\u001b[39m=\u001b[32m\"10\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 16v-4\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 8h.01\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<p\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm text-blue-700\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mThe global target price is used exclusively for liquidity pool operations. It does not affect user-facing exchange rates, which are set per package.\u001b[0m\n          \u001b[36m</p>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 space-y-6\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"bg-gray-50 rounded-lg p-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<h4\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm font-medium text-gray-700 mb-2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mCurrent Global Target Price\u001b[0m\n          \u001b[36m</h4>\u001b[39m\n          \u001b[36m<p\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-2xl font-bold text-gray-900\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0m2.0\u001b[0m\n            \u001b[0m \u001b[0m\n            \u001b[36m<span\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm font-normal text-gray-600\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mUSDT per BLOCKS\u001b[0m\n            \u001b[36m</span>\u001b[39m\n          \u001b[36m</p>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<label\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block text-sm font-medium text-gray-700\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[0mNew Global Target Price (USDT per 1 BLOCKS)\u001b[0m\n              \u001b[36m</label>\u001b[39m\n              \u001b[36m<input\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 min-h-[44px] text-base md:text-sm placeholder-gray-400 shadow-sm transition-colors focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500\"\u001b[39m\n                \u001b[33mmax\u001b[39m=\u001b[32m\"100\"\u001b[39m\n                \u001b[33mmin\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mplaceholder\u001b[39m=\u001b[32m\"2.0\"\u001b[39m\n                \u001b[33mstep\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mtype\u001b[39m=\u001b[32m\"number\"\u001b[39m\n                \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n              \u001b[36m/>\u001b[39m\n            \u001b[36m</div>\u001b[39m\n            \u001b[36m<p\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"text-xs text-gray-500 mt-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mThis price is used for liquidity pool operations only. It does not affect user token allocations.\u001b[0m\n            \u001b[36m</p>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<button\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500 px-6 py-3 text-sm min-h-[44px] opacity-50 cursor-not-allowed w-full\"\u001b[39m\n            \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mUpdate Global Target Price\u001b[0m\n          \u001b[36m</button>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n  \u001b[36m<w3m-modal\u001b[39m\n    \u001b[33mstyle\u001b[39m=\u001b[32m\"--local-border-bottom-mobile-radius: 0px;\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Proxy.waitForWrapper (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/wait-for.js:163:27)\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/components/GlobalTargetPriceManager.test.tsx:84:11\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "Error: Unable to find an element with the text: 2.0 USDT per 1 BLOCKS. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<w3m-modal\u001b[39m\n    \u001b[33mstyle\u001b[39m=\u001b[32m\"--local-border-bottom-mobile-radius: 0px;\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"bg-white/70 backdrop-blur-sm border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 pb-0 pb-4\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center space-x-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-dollar-sign h-5 w-5 text-blue-600\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<line\u001b[39m\n              \u001b[33mx1\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mx2\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33my1\u001b[39m=\u001b[32m\"2\"\u001b[39m\n              \u001b[33my2\u001b[39m=\u001b[32m\"22\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<h3\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-lg font-semibold text-gray-900\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mGlobal Target Price Management\u001b[0m\n          \u001b[36m</h3>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-start space-x-2 mt-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-info h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<circle\u001b[39m\n              \u001b[33mcx\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mcy\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mr\u001b[39m=\u001b[32m\"10\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 16v-4\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 8h.01\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<p\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm text-blue-700\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mThe global target price is used exclusively for liquidity pool operations. It does not affect user-facing exchange rates, which are set per package.\u001b[0m\n          \u001b[36m</p>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 space-y-6\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"bg-gray-50 rounded-lg p-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<h4\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm font-medium text-gray-700 mb-2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mCurrent Global Target Price\u001b[0m\n          \u001b[36m</h4>\u001b[39m\n          \u001b[36m<p\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-2xl font-bold text-gray-900\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0m2.0\u001b[0m\n            \u001b[0m \u001b[0m\n            \u001b[36m<span\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm font-normal text-gray-600\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mUSDT per BLOCKS\u001b[0m\n            \u001b[36m</span>\u001b[39m\n          \u001b[36m</p>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<label\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block text-sm font-medium text-gray-700\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[0mNew Global Target Price (USDT per 1 BLOCKS)\u001b[0m\n              \u001b[36m</label>\u001b[39m\n              \u001b[36m<input\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 min-h-[44px] text-base md:text-sm placeholder-gray-400 shadow-sm transition-colors focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500\"\u001b[39m\n                \u001b[33mmax\u001b[39m=\u001b[32m\"100\"\u001b[39m\n                \u001b[33mmin\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mplaceholder\u001b[39m=\u001b[32m\"2.0\"\u001b[39m\n                \u001b[33mstep\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mtype\u001b[39m=\u001b[32m\"number\"\u001b[39m\n                \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n              \u001b[36m/>\u001b[39m\n            \u001b[36m</div>\u001b[39m\n            \u001b[36m<p\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"text-xs text-gray-500 mt-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mThis price is used for liquidity pool operations only. It does not affect user token allocations.\u001b[0m\n            \u001b[36m</p>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<button\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500 px-6 py-3 text-sm min-h-[44px] opacity-50 cursor-not-allowed w-full\"\u001b[39m\n            \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mUpdate Global Target Price\u001b[0m\n          \u001b[36m</button>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Proxy.waitForWrapper (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/wait-for.js:163:27)\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/components/GlobalTargetPriceManager.test.tsx:84:11\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 75, "column": 3}, "meta": {}}, {"ancestorTitles": ["GlobalTargetPriceManager"], "fullName": "GlobalTargetPriceManager should handle wallet validation correctly", "status": "failed", "title": "should handle wallet validation correctly", "duration": 64.10462000000007, "failureMessages": ["TestingLibraryElementError: Found a label with the text of: New Global Target Price (USDT per 1 BLOCKS), however no form control was found associated to that label. Make sure you're using the \"for\" attribute or \"aria-labelledby\" attribute correctly.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<w3m-modal\u001b[39m\n    \u001b[33mstyle\u001b[39m=\u001b[32m\"--local-border-bottom-mobile-radius: 0px;\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"bg-white/70 backdrop-blur-sm border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 pb-0 pb-4\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center space-x-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-dollar-sign h-5 w-5 text-blue-600\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<line\u001b[39m\n              \u001b[33mx1\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mx2\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33my1\u001b[39m=\u001b[32m\"2\"\u001b[39m\n              \u001b[33my2\u001b[39m=\u001b[32m\"22\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<h3\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-lg font-semibold text-gray-900\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mGlobal Target Price Management\u001b[0m\n          \u001b[36m</h3>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-start space-x-2 mt-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-info h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<circle\u001b[39m\n              \u001b[33mcx\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mcy\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mr\u001b[39m=\u001b[32m\"10\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 16v-4\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 8h.01\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<p\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm text-blue-700\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mThe global target price is used exclusively for liquidity pool operations. It does not affect user-facing exchange rates, which are set per package.\u001b[0m\n          \u001b[36m</p>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 space-y-6\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"bg-gray-50 rounded-lg p-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<h4\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm font-medium text-gray-700 mb-2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mCurrent Global Target Price\u001b[0m\n          \u001b[36m</h4>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"animate-pulse\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"h-6 bg-gray-200 rounded w-24\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<label\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block text-sm font-medium text-gray-700\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[0mNew Global Target Price (USDT per 1 BLOCKS)\u001b[0m\n              \u001b[36m</label>\u001b[39m\n              \u001b[36m<input\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 min-h-[44px] text-base md:text-sm placeholder-gray-400 shadow-sm transition-colors focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500\"\u001b[39m\n                \u001b[33mmax\u001b[39m=\u001b[32m\"100\"\u001b[39m\n                \u001b[33mmin\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mplaceholder\u001b[39m=\u001b[32m\"2.0\"\u001b[39m\n                \u001b[33mstep\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mtype\u001b[39m=\u001b[32m\"number\"\u001b[39m\n                \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n              \u001b[36m/>\u001b[39m\n            \u001b[36m</div>\u001b[39m\n            \u001b[36m<p\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"text-xs text-gray-500 mt-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mThis price is used for liquidity pool operations only. It does not affect user token allocations.\u001b[0m\n            \u001b[36m</p>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<button\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500 px-6 py-3 text-sm min-h-[44px] opacity-50 cursor-not-allowed w-full\"\u001b[39m\n            \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mUpdate Global Target Price\u001b[0m\n          \u001b[36m</button>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/config.js:37:19)\n    at getAllByLabelText (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/queries/label-text.js:108:40)\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/query-helpers.js:52:17\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/query-helpers.js:95:19\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/components/GlobalTargetPriceManager.test.tsx:93:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "TestingLibraryElementError: Found a label with the text of: New Global Target Price (USDT per 1 BLOCKS), however no form control was found associated to that label. Make sure you're using the \"for\" attribute or \"aria-labelledby\" attribute correctly.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<w3m-modal\u001b[39m\n    \u001b[33mstyle\u001b[39m=\u001b[32m\"--local-border-bottom-mobile-radius: 0px;\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"bg-white/70 backdrop-blur-sm border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 pb-0 pb-4\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center space-x-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-dollar-sign h-5 w-5 text-blue-600\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<line\u001b[39m\n              \u001b[33mx1\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mx2\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33my1\u001b[39m=\u001b[32m\"2\"\u001b[39m\n              \u001b[33my2\u001b[39m=\u001b[32m\"22\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<h3\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-lg font-semibold text-gray-900\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mGlobal Target Price Management\u001b[0m\n          \u001b[36m</h3>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-start space-x-2 mt-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-info h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<circle\u001b[39m\n              \u001b[33mcx\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mcy\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mr\u001b[39m=\u001b[32m\"10\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 16v-4\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 8h.01\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<p\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm text-blue-700\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mThe global target price is used exclusively for liquidity pool operations. It does not affect user-facing exchange rates, which are set per package.\u001b[0m\n          \u001b[36m</p>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 space-y-6\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"bg-gray-50 rounded-lg p-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<h4\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm font-medium text-gray-700 mb-2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mCurrent Global Target Price\u001b[0m\n          \u001b[36m</h4>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"animate-pulse\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"h-6 bg-gray-200 rounded w-24\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<label\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block text-sm font-medium text-gray-700\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[0mNew Global Target Price (USDT per 1 BLOCKS)\u001b[0m\n              \u001b[36m</label>\u001b[39m\n              \u001b[36m<input\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 min-h-[44px] text-base md:text-sm placeholder-gray-400 shadow-sm transition-colors focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500\"\u001b[39m\n                \u001b[33mmax\u001b[39m=\u001b[32m\"100\"\u001b[39m\n                \u001b[33mmin\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mplaceholder\u001b[39m=\u001b[32m\"2.0\"\u001b[39m\n                \u001b[33mstep\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mtype\u001b[39m=\u001b[32m\"number\"\u001b[39m\n                \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n              \u001b[36m/>\u001b[39m\n            \u001b[36m</div>\u001b[39m\n            \u001b[36m<p\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"text-xs text-gray-500 mt-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mThis price is used for liquidity pool operations only. It does not affect user token allocations.\u001b[0m\n            \u001b[36m</p>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<button\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500 px-6 py-3 text-sm min-h-[44px] opacity-50 cursor-not-allowed w-full\"\u001b[39m\n            \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mUpdate Global Target Price\u001b[0m\n          \u001b[36m</button>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/config.js:37:19)\n    at getAllByLabelText (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/queries/label-text.js:108:40)\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/query-helpers.js:52:17\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/query-helpers.js:95:19\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/components/GlobalTargetPriceManager.test.tsx:93:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "TestingLibraryElementError: Found a label with the text of: New Global Target Price (USDT per 1 BLOCKS), however no form control was found associated to that label. Make sure you're using the \"for\" attribute or \"aria-labelledby\" attribute correctly.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<w3m-modal\u001b[39m\n    \u001b[33mstyle\u001b[39m=\u001b[32m\"--local-border-bottom-mobile-radius: 0px;\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"bg-white/70 backdrop-blur-sm border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 pb-0 pb-4\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center space-x-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-dollar-sign h-5 w-5 text-blue-600\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<line\u001b[39m\n              \u001b[33mx1\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mx2\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33my1\u001b[39m=\u001b[32m\"2\"\u001b[39m\n              \u001b[33my2\u001b[39m=\u001b[32m\"22\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<h3\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-lg font-semibold text-gray-900\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mGlobal Target Price Management\u001b[0m\n          \u001b[36m</h3>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-start space-x-2 mt-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-info h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<circle\u001b[39m\n              \u001b[33mcx\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mcy\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mr\u001b[39m=\u001b[32m\"10\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 16v-4\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 8h.01\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<p\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm text-blue-700\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mThe global target price is used exclusively for liquidity pool operations. It does not affect user-facing exchange rates, which are set per package.\u001b[0m\n          \u001b[36m</p>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 space-y-6\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"bg-gray-50 rounded-lg p-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<h4\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm font-medium text-gray-700 mb-2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mCurrent Global Target Price\u001b[0m\n          \u001b[36m</h4>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"animate-pulse\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"h-6 bg-gray-200 rounded w-24\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<label\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block text-sm font-medium text-gray-700\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[0mNew Global Target Price (USDT per 1 BLOCKS)\u001b[0m\n              \u001b[36m</label>\u001b[39m\n              \u001b[36m<input\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 min-h-[44px] text-base md:text-sm placeholder-gray-400 shadow-sm transition-colors focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500\"\u001b[39m\n                \u001b[33mmax\u001b[39m=\u001b[32m\"100\"\u001b[39m\n                \u001b[33mmin\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mplaceholder\u001b[39m=\u001b[32m\"2.0\"\u001b[39m\n                \u001b[33mstep\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mtype\u001b[39m=\u001b[32m\"number\"\u001b[39m\n                \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n              \u001b[36m/>\u001b[39m\n            \u001b[36m</div>\u001b[39m\n            \u001b[36m<p\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"text-xs text-gray-500 mt-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mThis price is used for liquidity pool operations only. It does not affect user token allocations.\u001b[0m\n            \u001b[36m</p>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<button\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500 px-6 py-3 text-sm min-h-[44px] opacity-50 cursor-not-allowed w-full\"\u001b[39m\n            \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mUpdate Global Target Price\u001b[0m\n          \u001b[36m</button>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/config.js:37:19)\n    at getAllByLabelText (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/queries/label-text.js:108:40)\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/query-helpers.js:52:17\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/query-helpers.js:95:19\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/components/GlobalTargetPriceManager.test.tsx:93:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 89, "column": 3}, "meta": {}}, {"ancestorTitles": ["GlobalTargetPriceManager"], "fullName": "GlobalTargetPriceManager should show error when wallet is not connected", "status": "failed", "title": "should show error when wallet is not connected", "duration": 8.110384999999951, "failureMessages": ["Error: Cannot find module '../../providers/Web3Provider'\nRequire stack:\n- /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/components/GlobalTargetPriceManager.test.tsx\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)\n    at Function._load (node:internal/modules/cjs/loader:1211:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/components/GlobalTargetPriceManager.test.tsx:117:15\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11", "Error: Cannot find module '../../providers/Web3Provider'\nRequire stack:\n- /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/components/GlobalTargetPriceManager.test.tsx\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)\n    at Function._load (node:internal/modules/cjs/loader:1211:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/components/GlobalTargetPriceManager.test.tsx:117:15\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11", "Error: Cannot find module '../../providers/Web3Provider'\nRequire stack:\n- /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/components/GlobalTargetPriceManager.test.tsx\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)\n    at Function._load (node:internal/modules/cjs/loader:1211:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/components/GlobalTargetPriceManager.test.tsx:117:15\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11"], "location": {"line": 108, "column": 3}, "meta": {}}, {"ancestorTitles": ["GlobalTargetPriceManager"], "fullName": "GlobalTargetPriceManager should validate input values correctly", "status": "failed", "title": "should validate input values correctly", "duration": 64.10723499999949, "failureMessages": ["TestingLibraryElementError: Found a label with the text of: New Global Target Price (USDT per 1 BLOCKS), however no form control was found associated to that label. Make sure you're using the \"for\" attribute or \"aria-labelledby\" attribute correctly.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<w3m-modal\u001b[39m\n    \u001b[33mstyle\u001b[39m=\u001b[32m\"--local-border-bottom-mobile-radius: 0px;\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"bg-white/70 backdrop-blur-sm border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 pb-0 pb-4\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center space-x-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-dollar-sign h-5 w-5 text-blue-600\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<line\u001b[39m\n              \u001b[33mx1\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mx2\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33my1\u001b[39m=\u001b[32m\"2\"\u001b[39m\n              \u001b[33my2\u001b[39m=\u001b[32m\"22\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<h3\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-lg font-semibold text-gray-900\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mGlobal Target Price Management\u001b[0m\n          \u001b[36m</h3>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-start space-x-2 mt-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-info h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<circle\u001b[39m\n              \u001b[33mcx\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mcy\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mr\u001b[39m=\u001b[32m\"10\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 16v-4\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 8h.01\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<p\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm text-blue-700\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mThe global target price is used exclusively for liquidity pool operations. It does not affect user-facing exchange rates, which are set per package.\u001b[0m\n          \u001b[36m</p>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 space-y-6\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"bg-gray-50 rounded-lg p-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<h4\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm font-medium text-gray-700 mb-2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mCurrent Global Target Price\u001b[0m\n          \u001b[36m</h4>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"animate-pulse\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"h-6 bg-gray-200 rounded w-24\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<label\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block text-sm font-medium text-gray-700\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[0mNew Global Target Price (USDT per 1 BLOCKS)\u001b[0m\n              \u001b[36m</label>\u001b[39m\n              \u001b[36m<input\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 min-h-[44px] text-base md:text-sm placeholder-gray-400 shadow-sm transition-colors focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500\"\u001b[39m\n                \u001b[33mmax\u001b[39m=\u001b[32m\"100\"\u001b[39m\n                \u001b[33mmin\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mplaceholder\u001b[39m=\u001b[32m\"2.0\"\u001b[39m\n                \u001b[33mstep\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mtype\u001b[39m=\u001b[32m\"number\"\u001b[39m\n                \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n              \u001b[36m/>\u001b[39m\n            \u001b[36m</div>\u001b[39m\n            \u001b[36m<p\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"text-xs text-gray-500 mt-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mThis price is used for liquidity pool operations only. It does not affect user token allocations.\u001b[0m\n            \u001b[36m</p>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<button\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500 px-6 py-3 text-sm min-h-[44px] opacity-50 cursor-not-allowed w-full\"\u001b[39m\n            \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mUpdate Global Target Price\u001b[0m\n          \u001b[36m</button>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/config.js:37:19)\n    at getAllByLabelText (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/queries/label-text.js:108:40)\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/query-helpers.js:52:17\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/query-helpers.js:95:19\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/components/GlobalTargetPriceManager.test.tsx:135:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "TestingLibraryElementError: Found a label with the text of: New Global Target Price (USDT per 1 BLOCKS), however no form control was found associated to that label. Make sure you're using the \"for\" attribute or \"aria-labelledby\" attribute correctly.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<w3m-modal\u001b[39m\n    \u001b[33mstyle\u001b[39m=\u001b[32m\"--local-border-bottom-mobile-radius: 0px;\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"bg-white/70 backdrop-blur-sm border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 pb-0 pb-4\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center space-x-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-dollar-sign h-5 w-5 text-blue-600\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<line\u001b[39m\n              \u001b[33mx1\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mx2\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33my1\u001b[39m=\u001b[32m\"2\"\u001b[39m\n              \u001b[33my2\u001b[39m=\u001b[32m\"22\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<h3\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-lg font-semibold text-gray-900\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mGlobal Target Price Management\u001b[0m\n          \u001b[36m</h3>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-start space-x-2 mt-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-info h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<circle\u001b[39m\n              \u001b[33mcx\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mcy\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mr\u001b[39m=\u001b[32m\"10\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 16v-4\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 8h.01\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<p\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm text-blue-700\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mThe global target price is used exclusively for liquidity pool operations. It does not affect user-facing exchange rates, which are set per package.\u001b[0m\n          \u001b[36m</p>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 space-y-6\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"bg-gray-50 rounded-lg p-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<h4\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm font-medium text-gray-700 mb-2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mCurrent Global Target Price\u001b[0m\n          \u001b[36m</h4>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"animate-pulse\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"h-6 bg-gray-200 rounded w-24\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<label\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block text-sm font-medium text-gray-700\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[0mNew Global Target Price (USDT per 1 BLOCKS)\u001b[0m\n              \u001b[36m</label>\u001b[39m\n              \u001b[36m<input\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 min-h-[44px] text-base md:text-sm placeholder-gray-400 shadow-sm transition-colors focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500\"\u001b[39m\n                \u001b[33mmax\u001b[39m=\u001b[32m\"100\"\u001b[39m\n                \u001b[33mmin\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mplaceholder\u001b[39m=\u001b[32m\"2.0\"\u001b[39m\n                \u001b[33mstep\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mtype\u001b[39m=\u001b[32m\"number\"\u001b[39m\n                \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n              \u001b[36m/>\u001b[39m\n            \u001b[36m</div>\u001b[39m\n            \u001b[36m<p\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"text-xs text-gray-500 mt-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mThis price is used for liquidity pool operations only. It does not affect user token allocations.\u001b[0m\n            \u001b[36m</p>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<button\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500 px-6 py-3 text-sm min-h-[44px] opacity-50 cursor-not-allowed w-full\"\u001b[39m\n            \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mUpdate Global Target Price\u001b[0m\n          \u001b[36m</button>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/config.js:37:19)\n    at getAllByLabelText (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/queries/label-text.js:108:40)\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/query-helpers.js:52:17\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/query-helpers.js:95:19\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/components/GlobalTargetPriceManager.test.tsx:135:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "TestingLibraryElementError: Found a label with the text of: New Global Target Price (USDT per 1 BLOCKS), however no form control was found associated to that label. Make sure you're using the \"for\" attribute or \"aria-labelledby\" attribute correctly.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<w3m-modal\u001b[39m\n    \u001b[33mstyle\u001b[39m=\u001b[32m\"--local-border-bottom-mobile-radius: 0px;\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"bg-white/70 backdrop-blur-sm border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 pb-0 pb-4\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center space-x-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-dollar-sign h-5 w-5 text-blue-600\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<line\u001b[39m\n              \u001b[33mx1\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mx2\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33my1\u001b[39m=\u001b[32m\"2\"\u001b[39m\n              \u001b[33my2\u001b[39m=\u001b[32m\"22\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<h3\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-lg font-semibold text-gray-900\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mGlobal Target Price Management\u001b[0m\n          \u001b[36m</h3>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-start space-x-2 mt-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-info h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<circle\u001b[39m\n              \u001b[33mcx\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mcy\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mr\u001b[39m=\u001b[32m\"10\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 16v-4\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 8h.01\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<p\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm text-blue-700\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mThe global target price is used exclusively for liquidity pool operations. It does not affect user-facing exchange rates, which are set per package.\u001b[0m\n          \u001b[36m</p>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 space-y-6\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"bg-gray-50 rounded-lg p-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<h4\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm font-medium text-gray-700 mb-2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mCurrent Global Target Price\u001b[0m\n          \u001b[36m</h4>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"animate-pulse\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"h-6 bg-gray-200 rounded w-24\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<label\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block text-sm font-medium text-gray-700\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[0mNew Global Target Price (USDT per 1 BLOCKS)\u001b[0m\n              \u001b[36m</label>\u001b[39m\n              \u001b[36m<input\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 min-h-[44px] text-base md:text-sm placeholder-gray-400 shadow-sm transition-colors focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500\"\u001b[39m\n                \u001b[33mmax\u001b[39m=\u001b[32m\"100\"\u001b[39m\n                \u001b[33mmin\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mplaceholder\u001b[39m=\u001b[32m\"2.0\"\u001b[39m\n                \u001b[33mstep\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mtype\u001b[39m=\u001b[32m\"number\"\u001b[39m\n                \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n              \u001b[36m/>\u001b[39m\n            \u001b[36m</div>\u001b[39m\n            \u001b[36m<p\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"text-xs text-gray-500 mt-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mThis price is used for liquidity pool operations only. It does not affect user token allocations.\u001b[0m\n            \u001b[36m</p>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<button\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500 px-6 py-3 text-sm min-h-[44px] opacity-50 cursor-not-allowed w-full\"\u001b[39m\n            \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mUpdate Global Target Price\u001b[0m\n          \u001b[36m</button>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/config.js:37:19)\n    at getAllByLabelText (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/queries/label-text.js:108:40)\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/query-helpers.js:52:17\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/query-helpers.js:95:19\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/components/GlobalTargetPriceManager.test.tsx:135:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 131, "column": 3}, "meta": {}}, {"ancestorTitles": ["GlobalTargetPriceManager"], "fullName": "GlobalTargetPriceManager should handle contract errors gracefully", "status": "failed", "title": "should handle contract errors gracefully", "duration": 32.21423800000048, "failureMessages": ["TestingLibraryElementError: Found a label with the text of: New Global Target Price (USDT per 1 BLOCKS), however no form control was found associated to that label. Make sure you're using the \"for\" attribute or \"aria-labelledby\" attribute correctly.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<w3m-modal\u001b[39m\n    \u001b[33mstyle\u001b[39m=\u001b[32m\"--local-border-bottom-mobile-radius: 0px;\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"bg-white/70 backdrop-blur-sm border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 pb-0 pb-4\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center space-x-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-dollar-sign h-5 w-5 text-blue-600\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<line\u001b[39m\n              \u001b[33mx1\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mx2\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33my1\u001b[39m=\u001b[32m\"2\"\u001b[39m\n              \u001b[33my2\u001b[39m=\u001b[32m\"22\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<h3\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-lg font-semibold text-gray-900\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mGlobal Target Price Management\u001b[0m\n          \u001b[36m</h3>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-start space-x-2 mt-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-info h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<circle\u001b[39m\n              \u001b[33mcx\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mcy\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mr\u001b[39m=\u001b[32m\"10\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 16v-4\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 8h.01\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<p\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm text-blue-700\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mThe global target price is used exclusively for liquidity pool operations. It does not affect user-facing exchange rates, which are set per package.\u001b[0m\n          \u001b[36m</p>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 space-y-6\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"bg-gray-50 rounded-lg p-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<h4\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm font-medium text-gray-700 mb-2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mCurrent Global Target Price\u001b[0m\n          \u001b[36m</h4>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"animate-pulse\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"h-6 bg-gray-200 rounded w-24\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<label\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block text-sm font-medium text-gray-700\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[0mNew Global Target Price (USDT per 1 BLOCKS)\u001b[0m\n              \u001b[36m</label>\u001b[39m\n              \u001b[36m<input\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 min-h-[44px] text-base md:text-sm placeholder-gray-400 shadow-sm transition-colors focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500\"\u001b[39m\n                \u001b[33mmax\u001b[39m=\u001b[32m\"100\"\u001b[39m\n                \u001b[33mmin\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mplaceholder\u001b[39m=\u001b[32m\"2.0\"\u001b[39m\n                \u001b[33mstep\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mtype\u001b[39m=\u001b[32m\"number\"\u001b[39m\n                \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n              \u001b[36m/>\u001b[39m\n            \u001b[36m</div>\u001b[39m\n            \u001b[36m<p\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"text-xs text-gray-500 mt-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mThis price is used for liquidity pool operations only. It does not affect user token allocations.\u001b[0m\n            \u001b[36m</p>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<button\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500 px-6 py-3 text-sm min-h-[44px] opacity-50 cursor-not-allowed w-full\"\u001b[39m\n            \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mUpdate Global Target Price\u001b[0m\n          \u001b[36m</button>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/config.js:37:19)\n    at getAllByLabelText (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/queries/label-text.js:108:40)\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/query-helpers.js:52:17\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/query-helpers.js:95:19\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/components/GlobalTargetPriceManager.test.tsx:153:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "TestingLibraryElementError: Found a label with the text of: New Global Target Price (USDT per 1 BLOCKS), however no form control was found associated to that label. Make sure you're using the \"for\" attribute or \"aria-labelledby\" attribute correctly.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<w3m-modal\u001b[39m\n    \u001b[33mstyle\u001b[39m=\u001b[32m\"--local-border-bottom-mobile-radius: 0px;\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"bg-white/70 backdrop-blur-sm border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 pb-0 pb-4\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center space-x-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-dollar-sign h-5 w-5 text-blue-600\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<line\u001b[39m\n              \u001b[33mx1\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mx2\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33my1\u001b[39m=\u001b[32m\"2\"\u001b[39m\n              \u001b[33my2\u001b[39m=\u001b[32m\"22\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<h3\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-lg font-semibold text-gray-900\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mGlobal Target Price Management\u001b[0m\n          \u001b[36m</h3>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-start space-x-2 mt-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-info h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<circle\u001b[39m\n              \u001b[33mcx\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mcy\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mr\u001b[39m=\u001b[32m\"10\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 16v-4\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 8h.01\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<p\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm text-blue-700\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mThe global target price is used exclusively for liquidity pool operations. It does not affect user-facing exchange rates, which are set per package.\u001b[0m\n          \u001b[36m</p>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 space-y-6\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"bg-gray-50 rounded-lg p-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<h4\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm font-medium text-gray-700 mb-2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mCurrent Global Target Price\u001b[0m\n          \u001b[36m</h4>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"animate-pulse\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"h-6 bg-gray-200 rounded w-24\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<label\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block text-sm font-medium text-gray-700\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[0mNew Global Target Price (USDT per 1 BLOCKS)\u001b[0m\n              \u001b[36m</label>\u001b[39m\n              \u001b[36m<input\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 min-h-[44px] text-base md:text-sm placeholder-gray-400 shadow-sm transition-colors focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500\"\u001b[39m\n                \u001b[33mmax\u001b[39m=\u001b[32m\"100\"\u001b[39m\n                \u001b[33mmin\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mplaceholder\u001b[39m=\u001b[32m\"2.0\"\u001b[39m\n                \u001b[33mstep\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mtype\u001b[39m=\u001b[32m\"number\"\u001b[39m\n                \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n              \u001b[36m/>\u001b[39m\n            \u001b[36m</div>\u001b[39m\n            \u001b[36m<p\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"text-xs text-gray-500 mt-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mThis price is used for liquidity pool operations only. It does not affect user token allocations.\u001b[0m\n            \u001b[36m</p>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<button\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500 px-6 py-3 text-sm min-h-[44px] opacity-50 cursor-not-allowed w-full\"\u001b[39m\n            \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mUpdate Global Target Price\u001b[0m\n          \u001b[36m</button>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/config.js:37:19)\n    at getAllByLabelText (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/queries/label-text.js:108:40)\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/query-helpers.js:52:17\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/query-helpers.js:95:19\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/components/GlobalTargetPriceManager.test.tsx:153:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "TestingLibraryElementError: Found a label with the text of: New Global Target Price (USDT per 1 BLOCKS), however no form control was found associated to that label. Make sure you're using the \"for\" attribute or \"aria-labelledby\" attribute correctly.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<w3m-modal\u001b[39m\n    \u001b[33mstyle\u001b[39m=\u001b[32m\"--local-border-bottom-mobile-radius: 0px;\"\u001b[39m\n  \u001b[36m/>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"bg-white/70 backdrop-blur-sm border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 pb-0 pb-4\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center space-x-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-dollar-sign h-5 w-5 text-blue-600\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<line\u001b[39m\n              \u001b[33mx1\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mx2\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33my1\u001b[39m=\u001b[32m\"2\"\u001b[39m\n              \u001b[33my2\u001b[39m=\u001b[32m\"22\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<h3\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-lg font-semibold text-gray-900\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mGlobal Target Price Management\u001b[0m\n          \u001b[36m</h3>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-start space-x-2 mt-2\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<svg\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"lucide lucide-info h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0\"\u001b[39m\n            \u001b[33mfill\u001b[39m=\u001b[32m\"none\"\u001b[39m\n            \u001b[33mheight\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mstroke\u001b[39m=\u001b[32m\"currentColor\"\u001b[39m\n            \u001b[33mstroke-linecap\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-linejoin\u001b[39m=\u001b[32m\"round\"\u001b[39m\n            \u001b[33mstroke-width\u001b[39m=\u001b[32m\"2\"\u001b[39m\n            \u001b[33mviewBox\u001b[39m=\u001b[32m\"0 0 24 24\"\u001b[39m\n            \u001b[33mwidth\u001b[39m=\u001b[32m\"24\"\u001b[39m\n            \u001b[33mxmlns\u001b[39m=\u001b[32m\"http://www.w3.org/2000/svg\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<circle\u001b[39m\n              \u001b[33mcx\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mcy\u001b[39m=\u001b[32m\"12\"\u001b[39m\n              \u001b[33mr\u001b[39m=\u001b[32m\"10\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 16v-4\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n            \u001b[36m<path\u001b[39m\n              \u001b[33md\u001b[39m=\u001b[32m\"M12 8h.01\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</svg>\u001b[39m\n          \u001b[36m<p\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm text-blue-700\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mThe global target price is used exclusively for liquidity pool operations. It does not affect user-facing exchange rates, which are set per package.\u001b[0m\n          \u001b[36m</p>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-6 space-y-6\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"bg-gray-50 rounded-lg p-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<h4\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"text-sm font-medium text-gray-700 mb-2\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mCurrent Global Target Price\u001b[0m\n          \u001b[36m</h4>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"animate-pulse\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"h-6 bg-gray-200 rounded w-24\"\u001b[39m\n            \u001b[36m/>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-4\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div>\u001b[39m\n            \u001b[36m<div\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"space-y-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<label\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block text-sm font-medium text-gray-700\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[0mNew Global Target Price (USDT per 1 BLOCKS)\u001b[0m\n              \u001b[36m</label>\u001b[39m\n              \u001b[36m<input\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 min-h-[44px] text-base md:text-sm placeholder-gray-400 shadow-sm transition-colors focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500\"\u001b[39m\n                \u001b[33mmax\u001b[39m=\u001b[32m\"100\"\u001b[39m\n                \u001b[33mmin\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mplaceholder\u001b[39m=\u001b[32m\"2.0\"\u001b[39m\n                \u001b[33mstep\u001b[39m=\u001b[32m\"0.01\"\u001b[39m\n                \u001b[33mtype\u001b[39m=\u001b[32m\"number\"\u001b[39m\n                \u001b[33mvalue\u001b[39m=\u001b[32m\"\"\u001b[39m\n              \u001b[36m/>\u001b[39m\n            \u001b[36m</div>\u001b[39m\n            \u001b[36m<p\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"text-xs text-gray-500 mt-1\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[0mThis price is used for liquidity pool operations only. It does not affect user token allocations.\u001b[0m\n            \u001b[36m</p>\u001b[39m\n          \u001b[36m</div>\u001b[39m\n          \u001b[36m<button\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500 px-6 py-3 text-sm min-h-[44px] opacity-50 cursor-not-allowed w-full\"\u001b[39m\n            \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[0mUpdate Global Target Price\u001b[0m\n          \u001b[36m</button>\u001b[39m\n        \u001b[36m</div>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/config.js:37:19)\n    at getAllByLabelText (/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/queries/label-text.js:108:40)\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/query-helpers.js:52:17\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@testing-library/dom/dist/query-helpers.js:95:19\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/components/GlobalTargetPriceManager.test.tsx:153:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 145, "column": 3}, "meta": {}}], "startTime": 1753820903411, "endTime": 1753820906827.2144, "status": "failed", "message": "", "name": "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/components/GlobalTargetPriceManager.test.tsx"}]}